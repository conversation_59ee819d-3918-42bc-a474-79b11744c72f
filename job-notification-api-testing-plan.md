# Job Notification API Testing Plan

## Overview

This document describes how to manually test the Job Notification Campaign and Recipient APIs, including how to verify:
- Campaign listing and detail
- Recipient listing and detail
- Email HTML rendering for a recipient

**Prerequisites:**
- The Laravel server is running (`php artisan serve` or via your web server)
- You have access to the API base URL (e.g., `http://localhost:8000/api`)
- There is at least one job notification campaign and recipient in your database

---

## 1. List Job Notification Campaigns

**Endpoint:**  
`GET /api/job-notification-campaigns`

**Description:**  
Returns a paginated list of all job notification campaigns, with summary info.

**How to Test:**
- <PERSON> Postman, HTTPie, or cURL:
  ```sh
  curl -X GET http://localhost:8000/api/job-notification-campaigns
  ```
- **Expected Result:**  
  - JSON array of campaigns
  - Each campaign includes fields like `id`, `job_id`, `job_title`, `status`, and `recipients_summary`

---

## 2. Get Campaign Detail

**Endpoint:**  
`GET /api/job-notification-campaigns/{id}`

**Description:**  
Returns detailed info for a specific campaign, including recipient summary.

**How to Test:**
- Replace `{id}` with a real campaign ID from the previous step.
- Example:
  ```sh
  curl -X GET http://localhost:8000/api/job-notification-campaigns/1
  ```
- **Expected Result:**  
  - JSON object with all campaign fields and a `recipients_summary` object

---

## 3. List Recipients for a Campaign

**Endpoint:**  
`GET /api/job-notification-campaigns/{id}/recipients`

**Description:**  
Returns a paginated list of all recipients for a given campaign.

**How to Test:**
- Replace `{id}` with a real campaign ID.
- Example:
  ```sh
  curl -X GET http://localhost:8000/api/job-notification-campaigns/1/recipients
  ```
- **Expected Result:**  
  - JSON array of recipients
  - Each recipient includes fields like `id`, `business_name`, `status`, `sent_at`, etc.

---

## 4. Get Recipient Detail

**Endpoint:**  
`GET /api/job-notification-recipients/{id}`

**Description:**  
Returns detailed info for a specific recipient, including campaign and business info.

**How to Test:**
- Replace `{id}` with a real recipient ID from the previous step.
- Example:
  ```sh
  curl -X GET http://localhost:8000/api/job-notification-recipients/1
  ```
- **Expected Result:**  
  - JSON object with all recipient fields, campaign info, and business info (if available)

---

## 5. Get Recipient Email HTML

**Endpoint:**  
`GET /api/job-notification-recipients/{id}/email`

**Description:**  
Returns the full HTML of the job notification email as it would be sent to the recipient.

**How to Test:**
- Replace `{id}` with a real recipient ID.
- Example:
  ```sh
  curl -X GET http://localhost:8000/api/job-notification-recipients/1/email
  ```
- **Expected Result:**  
  - Raw HTML output of the email (view in browser or save as `.html` to inspect)

---

## 6. Edge Cases & Validation

- Try invalid IDs (e.g., `/api/job-notification-campaigns/99999`) and verify you get a 404 error.
- Test pagination by adding `?per_page=2` to list endpoints.
- Test filtering on `/api/job-notification-campaigns?status=sent`.

---

## 7. Checklist

- [ ] Can list campaigns and see correct summary
- [ ] Can view campaign detail
- [ ] Can list recipients for a campaign
- [ ] Can view recipient detail (with campaign/business info)
- [ ] Can view recipient email HTML
- [ ] All endpoints return correct error for invalid IDs

---

## 8. Tips

- Use Postman's "Save Response" to keep a record of each test.
- For HTML email, open the response in a browser to check formatting.
- If you need authentication, add the appropriate headers (e.g., `Authorization: Bearer <token>`).

---

**If you find any issues, document the request, response, and error in this file for debugging.**

---

**End of Testing Plan**
