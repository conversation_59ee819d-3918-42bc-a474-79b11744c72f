<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class JobNotificationRecipientResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'job_notification_campaign_id' => $this->job_notification_campaign_id,
            'business_id' => $this->business_id,
            'business_name' => $this->business_name,
            'business_email' => $this->business_email,
            'business_phone' => $this->business_phone,
            'distance' => $this->distance,
            'status' => $this->status,
            'sent_at' => $this->sent_at,
            'failed_at' => $this->failed_at,
            'failure_reason' => $this->failure_reason,
            'open_count' => $this->open_count,
            'click_count' => $this->click_count,
            'last_opened_at' => $this->last_opened_at,
            'last_clicked_at' => $this->last_clicked_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            // Campaign info (basic)
            'campaign' => [
                'id' => $this->campaign->id,
                'job_id' => $this->campaign->job_id,
                'job_title' => $this->campaign->job_title,
                'status' => $this->campaign->status,
            ],
            // Business info (if loaded)
            'business' => $this->whenLoaded('business', function () {
                return [
                    'id' => $this->business->id,
                    'name' => $this->business->name,
                    'email' => $this->business->email,
                    'phone' => $this->business->phone,
                    'category' => $this->business->category,
                    'zip_code' => $this->business->zip_code,
                ];
            }),
        ];
    }
} 