<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class JobNotificationCampaignResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'job_id' => $this->job_id,
            'job_title' => $this->job_title,
            'job_description' => $this->job_description,
            'job_budget' => $this->job_budget,
            'job_zip_code' => $this->job_zip_code,
            'job_address' => $this->job_address,
            'job_latitude' => $this->job_latitude,
            'job_longitude' => $this->job_longitude,
            'job_category' => $this->job_category,
            'customer_name' => $this->customer_name,
            'customer_email' => $this->customer_email,
            'customer_phone' => $this->customer_phone,
            'search_radius' => $this->search_radius,
            'business_count' => $this->business_count,
            'status' => $this->status,
            'admin_token' => $this->admin_token,
            'token_expires_at' => $this->token_expires_at,
            'approved_at' => $this->approved_at,
            'rejected_at' => $this->rejected_at,
            'rejection_reason' => $this->rejection_reason,
            'sent_at' => $this->sent_at,
            'event_id' => $this->event_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            // Recipients summary (count by status)
            'recipients_summary' => [
                'total' => $this->recipients->count(),
                'pending' => $this->recipients->where('status', 'pending')->count(),
                'sent' => $this->recipients->where('status', 'sent')->count(),
                'failed' => $this->recipients->where('status', 'failed')->count(),
            ],
        ];
    }
} 