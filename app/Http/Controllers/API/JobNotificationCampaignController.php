<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\JobNotificationCampaign;
use App\Http\Resources\JobNotificationCampaignResource;
use Illuminate\Http\Request;

class JobNotificationCampaignController extends Controller
{
    /**
     * List job notification campaigns (paginated, filterable).
     */
    public function index(Request $request)
    {
        $query = JobNotificationCampaign::with('recipients');

        // Optional filters
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }
        if ($request->filled('job_id')) {
            $query->where('job_id', $request->input('job_id'));
        }
        if ($request->filled('title')) {
            $query->where('job_title', 'like', '%' . $request->input('title') . '%');
        }

        $perPage = $request->input('per_page', 15);
        $campaigns = $query->orderByDesc('created_at')->paginate($perPage);

        return JobNotificationCampaignResource::collection($campaigns);
    }

    /**
     * Show a single job notification campaign (with recipients summary).
     */
    public function show($id)
    {
        $campaign = JobNotificationCampaign::with('recipients')->findOrFail($id);
        return new JobNotificationCampaignResource($campaign);
    }
} 