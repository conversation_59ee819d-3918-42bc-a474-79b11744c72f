<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\JobNotificationCampaign;
use App\Http\Resources\JobNotificationCampaignResource;
use Illuminate\Http\Request;

class JobNotificationCampaignController extends Controller
{
    /**
     * List job notification campaigns (paginated, filterable, searchable).
     */
    public function index(Request $request)
    {
        $query = JobNotificationCampaign::with('recipients');

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->input('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('job_title', 'like', '%' . $searchTerm . '%')
                  ->orWhere('job_description', 'like', '%' . $searchTerm . '%')
                  ->orWhere('customer_name', 'like', '%' . $searchTerm . '%')
                  ->orWhere('customer_email', 'like', '%' . $searchTerm . '%')
                  ->orWhere('job_zip_code', 'like', '%' . $searchTerm . '%')
                  ->orWhere('job_address', 'like', '%' . $searchTerm . '%');
            });
        }

        // Optional filters
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }
        if ($request->filled('job_id')) {
            $query->where('job_id', $request->input('job_id'));
        }
        if ($request->filled('title')) {
            $query->where('job_title', 'like', '%' . $request->input('title') . '%');
        }

        // Pagination with limit and page parameters
        $limit = $request->input('limit', 15);
        $page = $request->input('page', 1);

        // Set current page for Laravel paginator
        \Illuminate\Pagination\Paginator::currentPageResolver(function () use ($page) {
            return $page;
        });

        $campaigns = $query->orderByDesc('created_at')->paginate($limit, ['*'], 'page', $page);

        return JobNotificationCampaignResource::collection($campaigns);
    }

    /**
     * Show a single job notification campaign (with recipients summary).
     */
    public function show($id)
    {
        $campaign = JobNotificationCampaign::with('recipients')->findOrFail($id);
        return new JobNotificationCampaignResource($campaign);
    }
} 