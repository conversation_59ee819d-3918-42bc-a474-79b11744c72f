<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\JobNotificationRecipient;
use App\Models\JobNotificationCampaign;
use App\Http\Resources\JobNotificationRecipientResource;
use Illuminate\Http\Request;
use App\Mail\JobNotificationMail;
use Illuminate\Support\Facades\Mail;

class JobNotificationRecipientController extends Controller
{
    /**
     * List all recipients for a campaign (paginated).
     */
    public function index(Request $request, $campaignId)
    {
        $campaign = JobNotificationCampaign::findOrFail($campaignId);
        $perPage = $request->input('per_page', 15);
        $recipients = $campaign->recipients()->with('business')->paginate($perPage);
        return JobNotificationRecipientResource::collection($recipients);
    }

    /**
     * Show detail for a recipient (with campaign and business info).
     */
    public function show($id)
    {
        $recipient = JobNotificationRecipient::with(['campaign', 'business'])->findOrFail($id);
        return new JobNotificationRecipientResource($recipient);
    }

    /**
     * Return the rendered HTML email for a recipient (using JobNotificationMail Mailable).
     */
    public function email($id)
    {
        $recipient = JobNotificationRecipient::with('campaign')->findOrFail($id);
        $campaign = $recipient->campaign;
        $mailable = new JobNotificationMail($campaign, $recipient);
        // Render the email to HTML
        $html = $mailable->render();
        return response($html)->header('Content-Type', 'text/html');
    }
} 